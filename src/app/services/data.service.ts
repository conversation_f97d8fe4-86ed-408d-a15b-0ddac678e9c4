import { Injectable } from '@angular/core';
import { RequestType, ResultType, UnviredCordovaSDK } from '@awesome-cordova-plugins/unvired-cordova-sdk/ngx';
import { AppConstants } from '../constants/appConstants';
import { RIG_HEADER } from 'src/models/RIG_HEADER';
import { Device } from '@awesome-cordova-plugins/device/ngx';
import { SiteRigModalComponent } from '../components/site-rig-modal/site-rig-modal.component';
import { PopoverController } from '@ionic/angular/standalone';
import { ReplaySubject, take, firstValueFrom } from 'rxjs';
import { selectPrefilledData, selectRigLoadedFromDb } from '../store/store.selector';
import { Store } from '@ngrx/store';
import { Observable } from 'rxjs';
import { TEMPLATE_HEADER } from 'src/models/TEMPLATE_HEADER';
import { HttpClient } from '@angular/common/http';
import { FORM_HEADER } from 'src/models/FORM_HEADER';
import * as RigActions from '../store/store.actions';
import moment from 'moment';
import { UtilityService } from './utility.service';
 declare var ump: any;
@Injectable({
  providedIn: 'root'
})
export class DataService {

     rigData$!: Observable<RIG_HEADER| null>;
     templates$!: Observable<TEMPLATE_HEADER[]| null>;

 private deviceId =''
  rigResult: any
  selectedServer : string = '';
  functionNames = [
  "FORMS_PA_CATEGORY_GET",
  "FORMS_PA_CUSTOMIZING_GET_V2",
  "FORMS_PA_HISTORY_GET",
  "FORMS_PA_HSE_STANDARD_GET",
  "FORMS_PA_TEMPLATE_GET",
  "FORMS_PA_HISTORY_GET",
  "FORMS_PA_CREW_GET_V2"
];


  inputArray = [
    {
      "name": "FORMS_PA_CATEGORY_GET",
      "input": {
        "INPUT_RIG_CONTEXT": [
          {
            "INPUT_RIG_CONTEXT_HEADER": {
              "RIG_NO": "CAN701O",
              "RIG_TYPE": "SHOP",
              "RIG_SUB_TYPE": "SHOP_OFFICE",
              "COMP_CODE": "1000",
              "DEVICE_NAME": "0300b83d-0800-7a87-26b3-000522780005"
            }
          }
        ]
      }
    },
    {
      "name": "FORMS_PA_CUSTOMIZING_GET_V2",
      "input": {
        "INPUT_RIG_CONTEXT": [
          {
            "INPUT_RIG_CONTEXT_HEADER": {
              "RIG_NO": "CAN701O",
              "RIG_TYPE": "SHOP",
              "RIG_SUB_TYPE": "SHOP_OFFICE",
              "COMP_CODE": "1000",
              "DEVICE_NAME": "0300b83d-0800-7a87-26b3-000522780005"
            }
          }
        ]
      }
    },

 {
      "name": "FORMS_PA_CATEGORY_GET",
      "input": {
        "INPUT_RIG_CONTEXT": [
          {
            "INPUT_RIG_CONTEXT_HEADER": {
              "RIG_NO": "CAN701O",
              "RIG_TYPE": "SHOP",
              "RIG_SUB_TYPE": "SHOP_OFFICE",
              "COMP_CODE": "1000",
              "DEVICE_NAME": "0300b83d-0800-7a87-26b3-000522780005"
            }
          }
        ]
      }
    },

    
 {
  "name": "FORMS_PA_HISTORY_GET",
  "input": {
    "INPUT_RIG_CONTEXT": [
      {
        "INPUT_RIG_CONTEXT_HEADER": {
          "RIG_NO": "CAN701O",
          "RIG_TYPE": "SHOP",
          "RIG_SUB_TYPE": "SHOP_OFFICE",
          "COMP_CODE": "1000",
          "DEVICE_NAME": "0300b83d-0800-7a87-26b3-000522780005"
        }
      }
    ]
  }
},
{
  "name": "FORMS_PA_HSE_STANDARD_GET",
  "input": {
    "INPUT_RIG_CONTEXT": [
      {
        "INPUT_RIG_CONTEXT_HEADER": {
          "RIG_NO": "CAN701O",
          "RIG_TYPE": "SHOP",
          "RIG_SUB_TYPE": "SHOP_OFFICE",
          "COMP_CODE": "1000",
          "DEVICE_NAME": "0300b83d-0800-7a87-26b3-000522780005"
        }
      }
    ]
  }
},
{
  "name": "FORMS_PA_TEMPLATE_GET",
  "input": {
    "INPUT_RIG_CONTEXT": [
      {
        "INPUT_RIG_CONTEXT_HEADER": {
          "RIG_NO": "CAN701O",
          "RIG_TYPE": "SHOP",
          "RIG_SUB_TYPE": "SHOP_OFFICE",
          "COMP_CODE": "1000",
          "DEVICE_NAME": "0300b83d-0800-7a87-26b3-000522780005"
        }
      }
    ]
  }
}
  ]

  inputData =[
    {
      "name": "FORMS_PA_CATEGORY_GET",
      "input": {
        "INPUT_RIG_CONTEXT": [
          {
            "INPUT_RIG_CONTEXT_HEADER": {
              "RIG_NO": "CAN701O",
              "RIG_TYPE": "SHOP",
              "RIG_SUB_TYPE": "SHOP_OFFICE",
              "COMP_CODE": "1000",
              "DEVICE_NAME": "0300b83d-0800-7a87-26b3-000522780005"
            }
          }
        ]
      }
    }
    
    
  ]
  


  constructor(private utilityService: UtilityService,private http: HttpClient ,private unviredSDK: UnviredCordovaSDK , private devicePlugin: Device, private popoverController: PopoverController, private store: Store) { 
    this.rigData$ = this.store.select(selectRigLoadedFromDb); 
  }

  public initialDataDownloaded$ = new ReplaySubject<void>(1);


    async getRigHeader(rigNo: string , deviceId: string) {
    let inputRigContext = {
      "INPUT_RIG_CONTEXT": [
        {
          "INPUT_RIG_CONTEXT_HEADER": {
            "RIG_NO": rigNo,
            "DEVICE_NAME": deviceId,
            // "LID": this.unviredSDK.guid()
          }
        }
      ]
    }

    console.log('inputrigcontext' , inputRigContext)
    try {
      let result = await this.unviredSDK.syncForeground(
        RequestType.PULL,

        '',
         inputRigContext,
        AppConstants.FORMS_PA_RIG_GET,
        true
      );
      if (result.type == ResultType.success) {
        await this.unviredSDK.dbSaveWebData();
    
          this.unviredSDK.logInfo(
          'DataService',
          'getRigHeader',
          'RigHeader Data has downloaded successfully.'
        );
      } else if (result.code && result.code === 401) {
  
        this.unviredSDK.logError(
          'DataService',
          'getRigHeader',
          'Error while downloading righeader data .'
        );
      } else {
        this.unviredSDK.logError(
          'DataService',
          'getRigHeader',
          'Error while downloading righeader data : ' + result.message
        );
      }
      return result.data;
    } catch (error) {
      this.unviredSDK.logError(
        'DataService',
        'getRigHeader',
        'Error while downloading righeader catch data : ' + error
      );
      let err = { type: ResultType.error, error: error };
      return err;
    }
  }



 async getRigHeaderFromDB(): Promise<any> {
    console.log('checkRigInSettingsTable')
    const query = `SELECT * FROM ${AppConstants.TABLE_RIG_HEADER} WHERE RIG_NO IS NOT NULL AND RIG_NO != ''`;

  
    try {
      const result = await this.unviredSDK.dbExecuteStatement(query);
      console.log('getRigHeader result after db call:', result.data[0]);
      if (result?.data?.length > 0) {
        this.rigResult = result.data[0]

        await ump.sendInitialDataDownloadRequest(this.inputDataForCustomization(result.data[0]));
        console.log('Emitting initialDataDownloaded$ event');
        this.initialDataDownloaded$.next(); // Notify listeners
        return result.data[0]; // Return first matching row
      } else {
          await this.openSiteNumberPopup();
       return null
       
      }
    } catch (error) {
     this.unviredSDK.logError(
        "HomePage",
        "checkRigHeaderTable",
        `Error while fetching site number from ${AppConstants.TABLE_RIG_HEADER}: ${JSON.stringify(error)}`
      );
      throw error;
    }
  }


  inputDataForCustomization(rigResult: RIG_HEADER){
    this.deviceId = this.devicePlugin.uuid
    console.log('the device id and rig no in input is ' , this.deviceId , rigResult.RIG_NO)
    let rigContextHeader = {
      RIG_NO: rigResult.RIG_NO,
      RIG_TYPE: rigResult.RIG_TYPE,
      RIG_SUB_TYPE: rigResult.RIG_SUB_TYPE,
      COMP_CODE: rigResult.COMP_CODE,
      DEVICE_NAME: this.deviceId
    };

    const inputArrayForCustomization = this.functionNames.map(name => ({
      name,
      input: {
        INPUT_RIG_CONTEXT: [
          {
            INPUT_RIG_CONTEXT_HEADER: rigContextHeader
          }
        ]
      }
    }));
    return inputArrayForCustomization;

  }

  async openSiteNumberPopup() {
    console.log('openSiteNumberPopup called from templates page');
    const modalRef = await this.popoverController.create({
      component: SiteRigModalComponent,
      cssClass: 'custom-site-modal',
      backdropDismiss: false,
      mode: 'md'
    });
  
    modalRef.onDidDismiss().then((result: any) => {
      console.log('Modal dismissed with data:', result);
      if (result.data) {
        console.log('Site number selected:', result.data);
        // Optional: reload rig data here or navigate
      }
    });
  
    await modalRef.present();
  }


  async getAllTemplatesFromDB(company: string , rigNo: string , rigType: string , rigSubType: string): Promise<any> {
    console.log('checkRigInSettingsTable')
    const query = ` SELECT TH.*, CH.NAME AS CATEGORY_DESC, TV.VER_ID AS L_VER_ID, MAX(TV.VER_NO)  AS L_VER_NO, TH.PBLSH_ON AS L_CRTD_ON, TA.ATTACHMENT_STATUS AS ATT_STATUS, FSA.NEXT_DUE AS NEXT_DUE  FROM TMPLT_HEADER AS TH, CATEGORY_HEADER AS CH, TMPLT_VER AS TV, TMPLT_ATTACHMENT AS TA, TMPLT_ASSGN AS TA, (Select ALERT_HEADER.NEXT_DUE, ALERT_HEADER.COMPANY, ALERT_HEADER.RIG_NO, TMPLT_HEADER.TMPLT_ID from TMPLT_HEADER left join ( SELECT * from FORM_SCHD_ALERT_HEADER  WHERE FORM_SCHD_ALERT_HEADER.COMPANY = ${company} AND FORM_SCHD_ALERT_HEADER.RIG_NO = '${rigNo}')  AS ALERT_HEADER on TMPLT_HEADER.TMPLT_ID = ALERT_HEADER.TMPLT_ID) AS FSA WHERE FSA.TMPLT_ID = TH.TMPLT_ID AND TH.CAT_ID = CH.CAT_ID AND TV.STATUS = 'REL' AND TH.IS_ACTIVE = 'true' AND TH.TMPLT_ID = TV.TMPLT_ID AND TA.TAG1 = TV.VER_ID AND TA.TMPLT_ID = TH.TMPLT_ID AND TA.TMPLT_ID IN (SELECT TMPLT_ID FROM TMPLT_ASSGN WHERE ASSGN_TYPE = '${rigType}' and VAL1 = '${rigSubType}') AND TA.TMPLT_ID NOT IN (SELECT TMPLT_ID FROM TMPLT_ASSGN WHERE ASSGN_TYPE = 'COMPANY' AND VAL1 != ${company} EXCEPT SELECT TMPLT_ID FROM TMPLT_ASSGN WHERE ASSGN_TYPE = 'COMPANY' AND VAL1 = ${company}) GROUP BY TH.TMPLT_ID ORDER BY CATEGORY_DESC`;

  console.log('getAllTemplatesFromDB query:', company , rigNo);
    try {
      const result = await this.unviredSDK.dbExecuteStatement(query);
      console.log('get table Header result after db call:', result.data);
      if (result?.data?.length > 0) {

     
        return result.data; // Return first matching row
      } else {
          // await this.openSiteNumberPopup();
        throw new Error("No Template data");
       
      }
    } catch (error) {
     this.unviredSDK.logError(
        "HomePage",
        "checkRigInSettingsTable",
        `Error while fetching site number from ${AppConstants.TABLE_SETTING_HEADER}: ${JSON.stringify(error)}`
      );
      throw error;
    }
  }

  async getCountOfTemplateAttachmentFromDB(){
 const query =   `SELECT COUNT(*) AS total_attachments FROM ${AppConstants.TABLE_TMPLT_ATTACHMENT};`
//  console.log('getAllTemplatesFromDB query:', company , rigNo);
 try {
   const result = await this.unviredSDK.dbExecuteStatement(query);
   console.log('get table Header result after db call:', result.data);
   if (result?.data?.length > 0) {

  
     return result.data; // Return first matching row
   } else {
       // await this.openSiteNumberPopup();
     throw new Error("No Template Attachment data");
    
   }
 } catch (error) {
  this.unviredSDK.logError(
     "HomePage",
     "checkRigInSettingsTable",
     `Error while fetching site number from ${AppConstants.TABLE_TMPLT_ATTACHMENT}: ${JSON.stringify(error)}`
   );
   throw error;
 }
  }


 async getProgressBarPercentage(){
    const query =   `SELECT ROUND((SUM(CASE WHEN ATTACHMENT_STATUS = 'DOWNLOADED' THEN 1 ELSE 0 END) * 100.0)/COUNT(*), 2) AS progress_percent FROM TMPLT_ATTACHMENT WHERE AUTO_DOWNLOAD = 'true';`

     try {
       const result = await this.unviredSDK.dbExecuteStatement(query);
      //  console.log('get percentage result after db call:', result.data[0].progress_percent
        // );
       if (result?.data?.length > 0) {
    
      
        const raw = result?.data?.[0]?.progress_percent;

        // Convert to number safely
        const percent = typeof raw === 'number' ? raw : parseFloat(raw);
        return isNaN(percent) ? 0 : percent;
       } else {
           // await this.openSiteNumberPopup();
         throw new Error("No Template Attachment data");
        
       }
     } catch (error) {
      this.unviredSDK.logError(
         "Templates page",
         "getProgressBarPercentage",
         `Error while fetching percentage progress from ${AppConstants.TABLE_TMPLT_ATTACHMENT}: ${JSON.stringify(error)}`
       );
       throw error;
     }
  }


  getPrefillData(): Observable<any> {
 
    return this.http.get('assets/prefill-data.json');
  }

  async getCompanyHeaderData(): Promise<any>{

      let query = `SELECT C.* FROM (SELECT COMP_CODE FROM RIG_HEADER) AS R LEFT JOIN (SELECT * FROM COMPANY_HEADER) AS C ON R.COMP_CODE = C.CODE`
      try {
        const companyData = await this.unviredSDK.dbExecuteStatement(query);
     if (companyData?.data?.length > 0) {
      console.log('get company result after db call:', companyData.data[0])
      return companyData.data[0]
     } else {
       throw new Error("No company data is present");
     }
   } catch (error) {
    this.unviredSDK.logError(
       "Company table ",
       "getCompanyHeaderData",
       `Error while fetching company data from ${AppConstants.TABLE_COMPANY_HEADER}: ${JSON.stringify(error)}`
     );
     throw error;
   }
    }



    async getCrewHeaderData(): Promise<any>{
      let query = `SELECT * FROM ${AppConstants.TABLE_CREW_HEADER} ORDER BY USER_NAME`
      try {
        const crewData = await this.unviredSDK.dbExecuteStatement(query);
        console.log('crew daata' , crewData)
     if (crewData?.data?.length > 0) {
      console.log('get Crew result after db call:', crewData.data)
      return crewData.data
     } else {
       throw new Error("No Crew data is present");
     }
   } catch (error) {
    this.unviredSDK.logError(
       "Crew table ",
       "getCrewHeaderData",
       `Error while fetching company data from ${AppConstants.TABLE_CREW_HEADER}: ${JSON.stringify(error)}`
     );
     throw error;
   }
    }


    async getOperatorHeaderData(): Promise<any>{
      let query = `SELECT * FROM ${AppConstants.TABLE_OPERATOR_HEADER} ORDER BY NAME`
      try {
        const operatorData = await this.unviredSDK.dbExecuteStatement(query);
     if (operatorData?.data?.length > 0) {
      return operatorData.data
     } else {
       throw new Error("No operatorData is present");
     }
   } catch (error) {
    this.unviredSDK.logError(
       "Operator table ",
       "getOperatorHeaderData",
       `Error while fetching operatorData from ${AppConstants.TABLE_OPERATOR_HEADER}: ${JSON.stringify(error)}`
     );
     throw error;
   }
    }


    async getNotifTypeHeaderData():Promise<any>{
      let query = `SELECT * FROM ${AppConstants.TABLE_C_NOTIF_TYPE_HEADER}`
      try {
        const notifTypeData = await this.unviredSDK.dbExecuteStatement(query);
     if (notifTypeData?.data?.length > 0) {
      console.log('get notifTypeData after db call:', notifTypeData.data)
      return notifTypeData.data
     } else {
       throw new Error("No notifTypeData is present");
     }
   } catch (error) {
    this.unviredSDK.logError(
       "Notif type table ",
       "getNotifTypeHeaderData",
       `Error while fetching TABLE_C_NOTIF_TYPE_HEADER from ${AppConstants.TABLE_C_NOTIF_TYPE_HEADER}: ${JSON.stringify(error)}`
     );
     throw error;
   }
    }

        async getPriorityHeaderData():Promise<any>{
      let query = `SELECT * FROM ${AppConstants.TABLE_C_PRIORITY_HEADER}`
      try {
        const priorityData = await this.unviredSDK.dbExecuteStatement(query);
     if (priorityData?.data?.length > 0) {
      console.log('get priorityData after db call:', priorityData.data)
      return priorityData.data
     } else {
       throw new Error("No priorityData is present");
     }
   } catch (error) {
    this.unviredSDK.logError(
       "Priority type table ",
       "getPriorityHeaderData",
       `Error while fetching TABLE_C_PRIORITY_HEADER from ${AppConstants.TABLE_C_PRIORITY_HEADER}: ${JSON.stringify(error)}`
     );
     throw error;
   }
    }


    


        async getCodeGroupHeaderData():Promise<any>{
      let query = `SELECT * FROM ${AppConstants.TABLE_C_CODE_GROUP_HEADER}`
      try {
        const codeGroupData = await this.unviredSDK.dbExecuteStatement(query);
     if (codeGroupData?.data?.length > 0) {
      console.log('get codeGroupData after db call:', codeGroupData.data)
      return codeGroupData.data
     } else {
       throw new Error("No codeGroupData is present");
     }
   } catch (error) {
    this.unviredSDK.logError(
       "Priority type table ",
       "getCodeGroupHeaderData",
       `Error while fetching TABLE_C_CODE_GROUP_HEADER from ${AppConstants.TABLE_C_CODE_GROUP_HEADER}: ${JSON.stringify(error)}`
     );
     throw error;
   }
    }


          async getCodeHeaderData():Promise<any>{
      let query = `SELECT * FROM ${AppConstants.TABLE_C_CODE_HEADER}`
      try {
        const codeData = await this.unviredSDK.dbExecuteStatement(query);
     if (codeData?.data?.length > 0) {
      console.log('get code header after db call:', codeData.data)
      return codeData.data
     } else {
       throw new Error("No codeData is present");
     }
   } catch (error) {
    this.unviredSDK.logError(
       "Code type table ",
       "getCodeHeaderData",
       `Error while fetching TABLE_C_CODE_HEADER from ${AppConstants.TABLE_C_CODE_HEADER}: ${JSON.stringify(error)}`
     );
     throw error;
   }
    }

  async getStmrHeaderData(): Promise<any> {
    let query = `SELECT * FROM ${AppConstants.TABLE_STMR_HEADER}`
    try {
      const stmrHeaderData = await this.unviredSDK.dbExecuteStatement(query);
      if (stmrHeaderData?.data?.length > 0) {
        console.log('get stmrHeaderData after db call:', stmrHeaderData.data)
        return stmrHeaderData.data
      } else {
        throw new Error("No stmrHeaderData is present");
      }
    } catch (error) {
      this.unviredSDK.logError(
        "Code type table ",
        "getStmrHeaderData",
        `Error while fetching TABLE_STMR_HEADER from ${AppConstants.TABLE_STMR_HEADER}: ${JSON.stringify(error)}`
      );
      throw error;
    }
  }

  async initialDataDownloadCall(){
       await ump.sendInitialDataDownloadRequest(this.inputDataForCustomization(this.rigResult));
  }


    returnFormsPageQuery(formLid?: string) {
    var query = "SELECT * FROM (SELECT FORM_DATA.DATA, FORM_DATA.LID AS DATA_LID, FORM_DATA.FID AS DATA_FID, FORM_HEADER.* FROM FORM_HEADER LEFT JOIN FORM_DATA ON FORM_HEADER.FORM_ID = FORM_DATA.FORM_ID ORDER BY FORM_HEADER.CRTD_ON DESC) AS A LEFT JOIN (SELECT CH.CAT_ID, CH.NAME AS CATEGORY_DESC, TV.TMPLT_ID, TH.DESCR AS TEMPLATE_DESC, TV.CRTD_ON AS PUBLISHED_ON, TV.VER_ID AS TMPLT_VERSION FROM TMPLT_HEADER AS TH, TMPLT_VER AS TV, CATEGORY_HEADER AS CH  WHERE TH.TMPLT_ID = TV.TMPLT_ID AND TH.TMPLT_ID IS NOT 'STMR' AND CH.CAT_ID = TH.CAT_ID) AS B ON A.VER_ID = B.TMPLT_VERSION where A.FORM_STATUS IS NOT 'DEL' AND A.P_MODE IS NOT 'D' ";

    // if(this.completedFormsIsChecked) {
    //   query += ` AND A.FORM_STATUS != 'SUBM' `;
    // }
  
    if (formLid) {
      query += ` AND LID = '${formLid}' `
    }

    query += ` ORDER BY CRTD_ON DESC`
    return query;

  }


  async getAllFormsFromTheDB(): Promise<any>{
     let query = this.returnFormsPageQuery()
    try {
      const formHeaderData = await this.unviredSDK.dbExecuteStatement(query);
      if (formHeaderData?.data?.length > 0) {
        console.log('get formHeaderData after db call:', formHeaderData.data)
        return formHeaderData.data
      } else {
        console.log("No formHeaderData is present");
      }
    } catch (error) {
      this.unviredSDK.logError(
        "Code type table ",
        "getAllFormsFromTheDB",
        `Error while fetching TABLE_FORM_HEADER from ${AppConstants.TABLE_FORM_HEADER}: ${JSON.stringify(error)}`
      );
      throw error;
    }
  }


async createFormHeader(template: TEMPLATE_HEADER) {
  let formHeader = new FORM_HEADER();

  // Get prefilled data from store
  const prefilledData = await firstValueFrom(this.store.select(selectPrefilledData).pipe(take(1)));

  formHeader.VER_ID = template.L_VER_ID;
  formHeader.LID = this.utilityService.guid32();
  formHeader.FORM_ID = 'New' + this.utilityService.guid32();
  formHeader.CRTD_BY = prefilledData?.USER_ID || '';
  formHeader.CRTD_ON = moment.utc().unix();
  formHeader.SUBM_BY = prefilledData?.USER_ID || '';
  formHeader.DATE_COMP = moment.utc().unix();
  formHeader.COMPANY = prefilledData?.COMP_CODE || '';
  formHeader.RIG_NO = prefilledData?.RIG_NO || '';
  formHeader.COMMENTS = "";
  formHeader.FORM_STATUS = AppConstants.VAL_FORM_STATUS.OPEN;
  formHeader.LAST_SYNC_USER = prefilledData?.USER_ID || '';
  formHeader.LAST_SYNC_TIME = moment.utc().unix();
  formHeader.P_MODE = "A";
  formHeader.TIME_ZONE = this.utilityService.getTimezone();
  formHeader.TEMPLATE_DESC = template.NAME;
  formHeader.CATEGORY_DESC = template.CATEGORY_DESC;
  formHeader.OBJECT_STATUS = AppConstants.OBJECT_STATUS.ADD;
  formHeader.SYNC_STATUS = AppConstants.SYNC_STATUS.NONE;

  return formHeader;
}


async createFormAndSaveToDB(template: TEMPLATE_HEADER) {
  let formHeader = await this.createFormHeader(template);
  console.log('formHeader in createFormAndSaveToDB is ' , formHeader);
  let formCreated = await this.unviredSDK.dbInsert(AppConstants.TABLE_FORM_HEADER, formHeader, AppConstants.BOOL_TRUE);
  if(formCreated.type === ResultType.success){
    this.store.dispatch(RigActions.loadAllFormsFromDb());
    return formHeader;
  }else{
    throw new Error("Error while creating form" + formCreated.error);
  }




}
//   async saveFormHeaderToServer(){
//      await this.unviredSdk.syncBackground(RequestType.RQST, inputHeader, "", AppConstants.FORMS_PA_FORM_SUBMIT, "FORM", formHeader.LID, AppConstants.BOOL_FALSE).subscribe( (result) => {

//         try {
//       let result = await this.unviredSDK.syncBackground(
//         RequestType.PULL,

//         '',
//          inputRigContext,
//         AppConstants.FORMS_PA_RIG_GET,
//         true
//       );
//       if (result.type == ResultType.success) {
//         await this.unviredSDK.dbSaveWebData();
    
//           this.unviredSDK.logInfo(
//           'DataService',
//           'getRigHeader',
//           'RigHeader Data has downloaded successfully.'
//         );
//       } else if (result.code && result.code === 401) {
  
//         this.unviredSDK.logError(
//           'DataService',
//           'getRigHeader',
//           'Error while downloading righeader data .'
//         );
//       } else {
//         this.unviredSDK.logError(
//           'DataService',
//           'getRigHeader',
//           'Error while downloading righeader data : ' + result.message
//         );
//       }
//       return result.data;
//     } catch (error) {
//       this.unviredSDK.logError(
//         'DataService',
//         'getRigHeader',
//         'Error while downloading righeader catch data : ' + error
//       );
//       let err = { type: ResultType.error, error: error };
//       return err;
//     }

//   }

    

   

  
  


  
 }
